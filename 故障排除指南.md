# MoreLogin自动化爬虫故障排除指南

## 常见错误：无法连接到Chrome调试端口

### 错误信息
```
无法连接到Chrome调试端口: websocket url timeout reached
```

### 可能原因和解决方案

#### 1. 环境启动但调试端口未就绪
**原因**: MoreLogin环境已启动，但Chrome调试端口还未完全可用

**解决方案**:
- 增加等待时间：在config.json中将`wait_time`从20增加到30-60秒
- 程序已自动增加5秒额外等待时间

#### 2. 调试端口配置错误
**原因**: 配置文件中的调试端口与实际端口不匹配

**解决方案**:
```json
{
  "morelogin": {
    "debug_port": "9222"  // 确保端口正确
  }
}
```

#### 3. 端口被占用
**原因**: 9222端口被其他程序占用

**解决方案**:
1. 检查端口占用：
   ```cmd
   netstat -ano | findstr :9222
   ```
2. 更换端口：
   ```json
   {
     "morelogin": {
       "debug_port": "9223"  // 使用其他端口
     }
   }
   ```

#### 4. MoreLogin环境未完全启动
**原因**: 环境状态显示running但实际未就绪

**解决方案**:
- 手动检查MoreLogin界面，确认环境已完全启动
- 增加配置中的`wait_time`值

#### 5. 防火墙阻止连接
**原因**: Windows防火墙阻止本地连接

**解决方案**:
- 临时关闭防火墙测试
- 添加程序到防火墙例外

### 调试步骤

#### 步骤1: 检查程序诊断信息
程序会自动输出诊断信息：
```
=== Chrome连接诊断 ===
配置的调试端口: 9222
API返回的调试端口: 9222
环境状态: running
进程ID: 12345
✓ 端口 9222 可以连接
=== 诊断结束 ===
```

#### 步骤2: 手动验证端口
在浏览器中访问：
```
http://127.0.0.1:9222/json
```
如果返回JSON数据，说明端口正常。

#### 步骤3: 检查MoreLogin状态
1. 打开MoreLogin界面
2. 确认目标环境状态为"运行中"
3. 检查是否有错误提示

#### 步骤4: 重启环境
如果以上都正常但仍无法连接：
1. 在MoreLogin界面手动停止环境
2. 等待10秒
3. 重新运行程序

### 配置优化建议

#### 增加超时时间
```json
{
  "morelogin": {
    "wait_time": 60  // 增加到60秒
  },
  "crawler": {
    "timeout": 60    // 增加页面超时
  }
}
```

#### 使用不同的调试端口
```json
{
  "morelogin": {
    "debug_port": "9223"  // 避免端口冲突
  }
}
```

### 高级故障排除

#### 检查网络连接
```cmd
telnet 127.0.0.1 9222
```

#### 查看详细日志
程序会输出详细的连接尝试日志，包括：
- 每次重试的详细信息
- 端口连接测试结果
- API状态查询结果

#### 手动测试API
```cmd
curl -X POST http://127.0.0.1:40000/api/env/status ^
  -H "Content-Type: application/json" ^
  -d "{\"envId\":\"你的环境ID\"}"
```

### 联系支持

如果以上方法都无法解决问题，请提供以下信息：
1. 完整的错误日志
2. config.json配置文件（隐藏敏感信息）
3. MoreLogin版本信息
4. 操作系统版本
5. 程序诊断输出

### 预防措施

1. **定期重启MoreLogin**: 长时间运行可能导致端口问题
2. **使用独立端口**: 为每个环境配置不同的调试端口
3. **监控系统资源**: 确保有足够的内存和CPU
4. **更新软件版本**: 使用最新版本的MoreLogin和程序
