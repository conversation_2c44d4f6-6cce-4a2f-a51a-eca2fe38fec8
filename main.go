package main

import (
    "bufio"
    "flag"
    "fmt"
    "log"
    "os"
    "os/signal"
    "syscall"
)

func main() {
    // 解析命令行参数
    var configFile = flag.String("config", "config.json", "配置文件路径")
    //var profileID = flag.String("profile", "", "MoreLogin配置文件ID")
    var showHelp = flag.Bool("help", false, "显示帮助信息")
    flag.Parse()

    if *showHelp {
        fmt.Println("MoreLogin自动化URL爬虫")
        fmt.Println("使用方法:")
        fmt.Println("  -config string    配置文件路径 (默认: config.json)")
        fmt.Println("  -profile string   MoreLogin配置文件ID")
        fmt.Println("  -help            显示此帮助信息")
        fmt.Println("")
        fmt.Println("示例:")
        fmt.Println("  go run . -profile 153342")
        fmt.Println("  go run . -config config_153342.json")
        return
    }

    log.Println("MoreLogin自动化URL爬虫启动...")

    // 步骤1: 根据配置文件中的配置，加载配置
    log.Println("=== 步骤1: 加载配置文件 ===")
    config, err := LoadConfig(*configFile)
    if err != nil {
        log.Printf("加载配置失败，使用默认配置: %v", err)
        config, _ = LoadConfig("") // 使用默认配置
    }
    log.Printf("✓ 配置加载完成")
    log.Printf("  - MoreLogin路径: %s", config.MoreLogin.ExecutePath)
    log.Printf("  - 环境ID: %s", config.MoreLogin.EnvID)
    log.Printf("  - API端点: %s", config.MoreLogin.APIEndpoint)
    log.Printf("  - URL文件: %s", config.Crawler.URLFile)

    // 创建高级爬虫实例
    crawler := NewAdvancedCrawler(config)
    defer crawler.Close()

    // 设置信号处理，优雅关闭
    sigChan := make(chan os.Signal, 1)
    signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
    go func() {
        <-sigChan
        log.Println("收到退出信号，正在关闭...")
        crawler.Close()
        os.Exit(0)
    }()

    // 步骤2: 从url.txt文件加载URL列表
    log.Println("\n=== 步骤2: 加载URL文件 ===")
    err = crawler.LoadURLs()
    if err != nil {
        log.Fatalf("加载URL文件失败: %v", err)
    }

    // 步骤3: 启动MoreLogin应用程序和环境
    log.Println("\n=== 步骤3: 启动MoreLogin ===")
    err = crawler.StartMoreLoginWithOptimizedFlow()
    if err != nil {
        log.Fatalf("启动MoreLogin失败: %v", err)
    }

    // 步骤4: 在浏览器窗口打开URL
    log.Println("\n=== 步骤4: 开始访问URL ===")
    err = crawler.CrawlAllURLs()
    if err != nil {
        log.Fatalf("爬取URL失败: %v", err)
    }

    log.Println("\n=== 程序执行完成 ===")
    log.Println("程序执行完成，按回车键退出...")
    bufio.NewReader(os.Stdin).ReadBytes('\n')
}
