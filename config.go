package main

import (
    "encoding/json"
    "fmt"
    "os"
)

// Config 应用程序配置
type Config struct {
    MoreLogin struct {
        ExecutePath  string `json:"execute_path"`
        EnvID        string `json:"env_id"`       // 环境ID，用于官方API
        APIID        string `json:"api_id"`       // API ID，用于官方API认证
        APIKey       string `json:"api_key"`      // API Key，用于官方API认证
        APIEndpoint  string `json:"api_endpoint"` // 本地APIURl
        DebugPort    string `json:"debug_port"`
        AutoStart    bool   `json:"auto_start"`
        WaitTime     int    `json:"wait_time"` // 等待浏览器启动的时间（秒）
        HeadlessMode bool   `json:"headless_mode"`
        WindowSize   string `json:"window_size"`
        UserDataDir  string `json:"user_data_dir"`
    } `json:"morelogin"`

    Crawler struct {
        DelayBetweenRequests int    `json:"delay_between_requests"` // 秒
        URLFile              string `json:"url_file"`
        MaxRetries           int    `json:"max_retries"`
        Timeout              int    `json:"timeout"` // 秒
        OpenInNewTab         bool   `json:"open_in_new_tab"`
        CloseTabAfterVisit   bool   `json:"close_tab_after_visit"`
        WaitForPageLoad      int    `json:"wait_for_page_load"` // 等待页面加载时间（秒）
        TakeScreenshot       bool   `json:"take_screenshot"`
        ScreenshotDir        string `json:"screenshot_dir"`
    } `json:"crawler"`

    Logging struct {
        Level   string `json:"level"`
        LogFile string `json:"log_file"`
        Console bool   `json:"console"`
    } `json:"logging"`
}

// LoadConfig 从文件加载配置
func LoadConfig(filename string) (*Config, error) {
    config := &Config{}

    // 设置默认值
    config.MoreLogin.DebugPort = "9222"
    config.MoreLogin.APIEndpoint = "http://127.0.0.1:40000"
    config.MoreLogin.AutoStart = true
    config.MoreLogin.WaitTime = 15
    config.MoreLogin.HeadlessMode = false
    config.MoreLogin.WindowSize = "1920,1080"

    config.Crawler.DelayBetweenRequests = 3
    config.Crawler.URLFile = "url.txt"
    config.Crawler.MaxRetries = 3
    config.Crawler.Timeout = 30
    config.Crawler.OpenInNewTab = true
    config.Crawler.CloseTabAfterVisit = false
    config.Crawler.WaitForPageLoad = 3
    config.Crawler.TakeScreenshot = false
    config.Crawler.ScreenshotDir = "screenshots"

    config.Logging.Level = "info"
    config.Logging.Console = true

    // 如果配置文件存在，则加载
    if _, err := os.Stat(filename); err == nil {
        file, err := os.Open(filename)
        if err != nil {
            return nil, fmt.Errorf("无法打开配置文件: %v", err)
        }
        defer file.Close()

        decoder := json.NewDecoder(file)
        if err = decoder.Decode(config); err != nil {
            return nil, fmt.Errorf("解析配置文件失败: %v", err)
        }
    }

    return config, nil
}

// SaveConfig 保存配置到文件
func (c *Config) SaveConfig(filename string) error {
    file, err := os.Create(filename)
    if err != nil {
        return fmt.Errorf("无法创建配置文件: %v", err)
    }
    defer file.Close()

    encoder := json.NewEncoder(file)
    encoder.SetIndent("", "  ")
    if err = encoder.Encode(c); err != nil {
        return fmt.Errorf("保存配置文件失败: %v", err)
    }

    return nil
}
