# MoreLogin故障排除指南

## 🚨 常见错误及解决方案

### 1. API连接错误 (状态码400)

**错误信息:**
```
启动MoreLogin失败: 确保MoreLogin运行失败: MoreLogin启动后API仍不可用: API不健康，状态码: 400
```

**原因分析:**
- 状态码400表示请求格式有问题，但API端点是可访问的
- MoreLogin应用程序已在运行，但API端点可能不是预期的格式

**解决方案:**

#### 方案1: 使用强制启动模式
```cmd
run_force_start.bat
```
这个脚本会跳过API健康检查，直接尝试启动环境。

#### 方案2: 检查API端点配置
确认config.json中的API端点是否正确：
```json
{
  "morelogin": {
    "api_endpoint": "http://127.0.0.1:40000"
  }
}
```

#### 方案3: 手动启动MoreLogin
1. 手动启动MoreLogin应用程序
2. 确认本地API服务已启动
3. 然后运行爬虫程序

### 2. 端口连接被拒绝

**错误信息:**
```
dial tcp 127.0.0.1:40000: connectex: No connection could be made because the target machine actively refused it.
```

**解决方案:**
1. 检查MoreLogin是否已启动
2. 确认端口40000未被其他程序占用
3. 检查防火墙设置

### 3. 环境启动失败

**错误信息:**
```
启动环境失败: 环境不存在
```

**解决方案:**
1. 确认环境ID是否正确
2. 检查环境是否已被删除
3. 验证API认证信息

## 🔧 调试步骤

### 步骤1: 检查MoreLogin状态
```cmd
# 检查MoreLogin进程是否运行
tasklist | findstr MoreLogin
```

### 步骤2: 测试API连接
使用浏览器或curl测试API端点：
```bash
curl http://127.0.0.1:40000/
```

### 步骤3: 检查配置文件
确认config.json中的所有配置项：
```json
{
  "morelogin": {
    "execute_path": "D:\\Users\\Administrator\\MoreLoginPlus\\2.41.1.0\\MoreLogin.exe",
    "env_id": "1958771062629539840",
    "api_id": "1642313569211767",
    "api_key": "53392bd9f81342abb2ee0e3a0875a837",
    "api_endpoint": "http://127.0.0.1:40000",
    "wait_time": 30
  }
}
```

### 步骤4: 查看详细日志
检查crawler.log文件中的详细错误信息。

## 🛠️ 修复措施

### 已实现的修复

1. **更宽容的API检查**
   - 状态码400不再被视为致命错误
   - 改为警告并继续尝试启动环境

2. **重试机制**
   - 环境启动失败时自动重试3次
   - 每次重试间隔5秒

3. **简化的连接检查**
   - 使用更简单的连接测试方法
   - 只要能连接到端口就认为MoreLogin在运行

4. **详细的错误日志**
   - 显示具体的API端点和参数
   - 记录每次重试的详细信息

### 配置优化

1. **增加等待时间**
   - 将wait_time从15秒增加到30秒
   - 给MoreLogin更多时间完全启动

2. **API端点验证**
   - 确认使用正确的本地API地址
   - 支持不同的API端点格式

## 🚀 推荐启动流程

### 方法1: 强制启动 (推荐)
```cmd
run_force_start.bat
```

### 方法2: 手动启动
1. 先手动启动MoreLogin
2. 等待完全加载
3. 运行: `go run .`

### 方法3: 分步调试
1. 检查MoreLogin进程
2. 测试API连接
3. 验证配置文件
4. 运行爬虫程序

## 📞 获取帮助

如果问题仍然存在，请提供以下信息：

1. **完整的错误日志**
2. **config.json配置内容**
3. **MoreLogin版本信息**
4. **系统环境信息**
5. **网络和防火墙设置**

## 🔄 版本兼容性

确保使用的MoreLogin版本支持本地API功能：
- MoreLogin 2.41.1.0 ✓
- 更早版本可能不支持某些API端点

## 💡 最佳实践

1. **定期更新MoreLogin**到最新版本
2. **保持配置文件**的准确性
3. **监控系统资源**使用情况
4. **定期清理日志**文件
5. **备份重要配置**信息
