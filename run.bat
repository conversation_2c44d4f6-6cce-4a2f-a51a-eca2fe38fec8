@echo off
echo MoreLogin自动化URL爬虫 v2.1 - 优化版
echo ========================================

REM 检查Go是否安装
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Go语言环境，请先安装Go
    pause
    exit /b 1
)

REM 检查url.txt文件是否存在
if not exist "url.txt" (
    echo 错误: 未找到url.txt文件
    echo 请创建url.txt文件并添加要访问的URL
    echo.
    echo 示例url.txt内容:
    echo # 这是注释行
    echo https://www.google.com
    echo https://www.github.com
    pause
    exit /b 1
)

REM 检查config.json文件是否存在
if not exist "config.json" (
    echo 警告: 未找到config.json文件，将使用默认配置
    echo 建议创建config.json文件以配置MoreLogin路径和API信息
)

REM 安装依赖
echo 正在安装依赖...
go mod tidy

REM 编译程序
echo 正在编译程序...
go build -o crawler.exe .
if %errorlevel% neq 0 (
    echo 编译失败
    pause
    exit /b 1
)

echo.
echo 优化后的执行流程:
echo 1. 加载配置文件 (config.json)
echo 2. 读取URL文件 (url.txt)
echo 3. 启动MoreLogin应用程序
echo 4. 启动指定环境 (通过API)
echo 5. 检查环境状态
echo 6. 在浏览器中访问URL
echo.
echo 功能特性:
echo - 优化的启动流程，确保每步都成功
echo - 详细的步骤日志和进度显示
echo - 智能重试和错误处理
echo - 完整的访问统计报告
echo.

REM 运行程序
echo 启动优化版爬虫...
crawler.exe

pause
