# MoreLogin自动化URL爬虫 - 优化执行流程

## 概述
本程序已优化为按照指定顺序执行的自动化流程，确保每个步骤都能正确完成后再进行下一步。

## 优化后的执行顺序

### 步骤1: 加载配置文件
- 读取 `config.json` 配置文件
- 验证MoreLogin路径、环境ID、API配置等关键参数
- 显示配置摘要信息

### 步骤2: 加载URL文件
- 读取 `url.txt` 文件中的URL列表
- 验证URL格式的有效性
- 跳过空行和注释行（以#开头）
- 显示有效URL数量和详细列表

### 步骤3: 启动MoreLogin
#### 3.1 启动MoreLogin应用程序
- 根据配置文件中的路径启动MoreLogin主程序
- 等待应用程序完全启动

#### 3.2 调用StartEnvironment()启动指定环境
- 使用官方API启动指定的环境ID
- 支持重试机制（最多3次）
- 发送启动命令到MoreLogin

#### 3.3 检查环境是否启动
- 持续检查环境状态直到变为"running"
- 等待环境完全准备就绪
- 超时保护机制

#### 3.4 创建浏览器连接
- 建立与MoreLogin浏览器的调试连接
- 验证连接是否正常
- 准备开始访问URL

### 步骤4: 在浏览器窗口打开URL
#### 4.1 开始访问URL
- 按顺序访问url.txt中的每个URL
- 每个URL之间有配置的延迟时间
- 支持失败重试机制
- 实时显示访问进度和结果

#### 4.2 生成访问摘要
- 统计成功和失败的URL数量
- 计算总耗时和平均耗时
- 列出所有失败的URL
- 显示成功率百分比

## 配置文件说明

### config.json 关键配置项
```json
{
  "morelogin": {
    "execute_path": "MoreLogin.exe的完整路径",
    "env_id": "环境ID",
    "api_id": "API ID",
    "api_key": "API密钥",
    "api_endpoint": "API端点地址",
    "wait_time": 20
  },
  "crawler": {
    "delay_between_requests": 1,
    "url_file": "url.txt",
    "max_retries": 3,
    "timeout": 30
  }
}
```

### url.txt 格式说明
```
# 这是注释行，会被跳过
https://example1.com
https://example2.com
# 另一个注释
https://example3.com
```

## 使用方法

1. 确保配置文件 `config.json` 正确配置
2. 在 `url.txt` 文件中添加要访问的URL
3. 运行程序：
   ```bash
   go run .
   ```
   或使用批处理文件：
   ```bash
   run.bat
   ```

## 日志输出示例

```
=== 步骤1: 加载配置文件 ===
✓ 配置加载完成
  - MoreLogin路径: D:\MoreLogin\MoreLogin.exe
  - 环境ID: 1958771062629539840
  - API端点: http://127.0.0.1:40000

=== 步骤2: 加载URL文件 ===
2.1 - 打开URL文件: url.txt
2.2 - 解析URL文件内容...
  [1] https://example1.com
  [2] https://example2.com
✓ URL文件解析完成
  - 总行数: 3
  - 有效URL: 2
  - 跳过行数: 1

=== 步骤3: 启动MoreLogin ===
3.1 - 启动MoreLogin应用程序...
✓ MoreLogin应用程序启动成功
3.2 - 启动指定环境...
✓ 环境启动命令发送成功
3.3 - 检查环境启动状态...
✓ 环境已成功启动并准备就绪
3.4 - 创建浏览器连接...
✓ 浏览器连接创建成功，准备访问URL

=== 步骤4: 开始访问URL ===
4.1 - 开始在浏览器中访问 2 个URL
--- 访问进度: 1/2 ---
✓ URL访问成功: https://example1.com
--- 访问进度: 2/2 ---
✓ URL访问成功: https://example2.com

4.2 - 所有URL访问完成，生成访问摘要...

=== URL访问摘要报告 ===
总URL数量: 2
✓ 成功访问: 2 (100.0%)
✗ 访问失败: 0 (0.0%)
总耗时: 5.2s
平均耗时: 2.6s
=== 报告结束 ===
```

## 错误处理

- 每个步骤都有详细的错误信息
- 支持重试机制
- 超时保护
- 优雅的程序退出

## 注意事项

1. 确保MoreLogin已正确安装并配置
2. 确保API凭据正确
3. 确保网络连接正常
4. 建议在测试环境中先验证配置
