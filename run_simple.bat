@echo off
echo MoreLogin简化启动模式
echo ====================

echo.
echo 此模式特点:
echo - 跳过复杂的API健康检查
echo - 直接尝试启动环境
echo - 自动重试机制
echo - 更宽容的错误处理
echo.

REM 检查必要文件
if not exist "config.json" (
    echo 错误: 未找到config.json文件
    pause
    exit /b 1
)

if not exist "url.txt" (
    echo 错误: 未找到url.txt文件
    pause
    exit /b 1
)

echo 配置信息:
echo - 环境ID: 1958771062629539840
echo - API端点: http://127.0.0.1:40000
echo - 等待时间: 30秒
echo - 重试次数: 3次
echo.

REM 编译程序
echo 正在编译程序...
go build -o crawler.exe . 2>nul
if %errorlevel% neq 0 (
    echo 编译失败，尝试安装依赖...
    go mod tidy
    go build -o crawler.exe .
    if %errorlevel% neq 0 (
        echo 编译仍然失败
        pause
        exit /b 1
    )
)

echo ✓ 编译成功
echo.

echo 开始启动流程...
echo ==================

REM 运行程序
crawler.exe

echo.
echo 程序执行完成
echo.
echo 如果遇到问题，请查看:
echo - 控制台输出信息
echo - crawler.log日志文件
echo - TROUBLESHOOTING.md故障排除指南
echo.

pause
