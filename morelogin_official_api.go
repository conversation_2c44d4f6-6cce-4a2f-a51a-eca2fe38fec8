package main

import (
    "bytes"
    "crypto/md5"
    "encoding/hex"
    "encoding/json"
    "fmt"
    "io"
    "log"
    "net/http"
    "strconv"
    "time"

    "github.com/google/uuid"
)

// MoreLoginOfficialAPI MoreLogin官方API客户端
type MoreLoginOfficialAPI struct {
    BaseURL    string
    APIID      string
    APIKey     string
    HTTPClient *http.Client
}

// StartEnvRequest 启动环境请求
type StartEnvRequest struct {
    EnvID string `json:"envId"`
}

// StartEnvResponse 启动环境响应
type StartEnvResponse struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Data    struct {
        EnvID     string `json:"envId"`
        Status    string `json:"status"`
        DebugPort string `json:"debugPort"`
        ProcessID int    `json:"processId"`
    } `json:"data"`
}

// StopEnvRequest 停止环境请求
type StopEnvRequest struct {
    EnvID string `json:"envId"`
}

// NewMoreLoginOfficialAPI 创建MoreLogin官方API客户端
func NewMoreLoginOfficialAPI(baseURL, apiID, apiKey string) *MoreLoginOfficialAPI {
    return &MoreLoginOfficialAPI{
        BaseURL: baseURL,
        APIID:   apiID,
        APIKey:  apiKey,
        HTTPClient: &http.Client{
            Timeout: 60 * time.Second,
        },
    }
}

// generateNonceID 生成Nonce ID
func (api *MoreLoginOfficialAPI) generateNonceID() string {
    timestamp := strconv.FormatInt(time.Now().Unix(), 10)
    randomUUID := uuid.New().String()
    return timestamp + ":" + randomUUID
}

// generateAuthorization 生成Authorization签名
func (api *MoreLoginOfficialAPI) generateAuthorization(nonceID string) string {
    // MD5(api_id + nonce_id + api_key)
    data := api.APIID + nonceID + api.APIKey
    hash := md5.Sum([]byte(data))
    return hex.EncodeToString(hash[:])
}

// makeOfficialRequest 发送官方API请求
func (api *MoreLoginOfficialAPI) makeOfficialRequest(method, endpoint string, payload interface{}) (*http.Response, error) {
    url := api.BaseURL + endpoint

    var body io.Reader
    if payload != nil {
        jsonData, err := json.Marshal(payload)
        if err != nil {
            return nil, fmt.Errorf("序列化请求数据失败: %v", err)
        }
        body = bytes.NewBuffer(jsonData)
        log.Printf("请求数据: %s", string(jsonData))
    }

    req, err := http.NewRequest(method, url, body)
    if err != nil {
        return nil, fmt.Errorf("创建请求失败: %v", err)
    }

    // 生成认证头
    nonceID := api.generateNonceID()
    authorization := api.generateAuthorization(nonceID)

    // 设置请求头
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("X-Api-Id", api.APIID)
    req.Header.Set("X-Nonce-Id", nonceID)
    req.Header.Set("Authorization", authorization)
    req.Header.Set("User-Agent", "MoreLogin-Crawler/2.0")

    log.Printf("请求URL: %s", url)
    log.Printf("请求头: X-Api-Id=%s, X-Nonce-Id=%s, Authorization=%s", api.APIID, nonceID, authorization)

    resp, err := api.HTTPClient.Do(req)
    if err != nil {
        return nil, fmt.Errorf("发送请求失败: %v", err)
    }

    return resp, nil
}

// StartEnvironment 启动指定环境
func (api *MoreLoginOfficialAPI) StartEnvironment(envID string) (*StartEnvResponse, error) {
    log.Printf("正在通过官方API启动环境: %s", envID)

    request := StartEnvRequest{
        EnvID: envID,
    }

    resp, err := api.makeOfficialRequest("POST", "/api/env/start", request)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()

    // 读取响应内容
    respBody, err := io.ReadAll(resp.Body)
    if err != nil {
        return nil, fmt.Errorf("读取响应失败: %v", err)
    }

    log.Printf("API响应状态: %d", resp.StatusCode)
    log.Printf("API响应内容: %s", string(respBody))

    var startResp StartEnvResponse
    if err := json.Unmarshal(respBody, &startResp); err != nil {
        return nil, fmt.Errorf("解析启动响应失败: %v", err)
    }

    if startResp.Code != 0 {
        return nil, fmt.Errorf("启动环境失败: %s (代码: %d)", startResp.Message, startResp.Code)
    }

    log.Printf("✓ 环境启动成功: %s, 状态: %s", envID, startResp.Data.Status)
    return &startResp, nil
}

// StopEnvironment 停止指定环境
func (api *MoreLoginOfficialAPI) StopEnvironment(envID string) error {
    log.Printf("正在通过官方API停止环境: %s", envID)

    request := StopEnvRequest{
        EnvID: envID,
    }

    resp, err := api.makeOfficialRequest("POST", "/api/env/close", request)
    if err != nil {
        return err
    }
    defer resp.Body.Close()

    // 读取响应内容
    respBody, err := io.ReadAll(resp.Body)
    if err != nil {
        return fmt.Errorf("读取响应失败: %v", err)
    }

    log.Printf("停止环境API响应: %s", string(respBody))

    var apiResp StartEnvResponse
    if err := json.Unmarshal(respBody, &apiResp); err != nil {
        return fmt.Errorf("解析停止响应失败: %v", err)
    }

    if apiResp.Code != 0 {
        return fmt.Errorf("停止环境失败: %s (代码: %d)", apiResp.Message, apiResp.Code)
    }

    log.Printf("✓ 环境停止成功: %s", envID)
    return nil
}

// GetEnvironmentStatus 获取环境状态
func (api *MoreLoginOfficialAPI) GetEnvironmentStatus(envID string) (*StartEnvResponse, error) {
    //endpoint := fmt.Sprintf("/api/env/status?envId=%s", envID)
    request := StartEnvRequest{
        EnvID: envID,
    }
    resp, err := api.makeOfficialRequest("POST", "/api/env/status", request)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()

    // 读取响应内容
    respBody, err := io.ReadAll(resp.Body)
    if err != nil {
        return nil, fmt.Errorf("读取响应失败: %v", err)
    }

    log.Printf("环境状态API响应: %s", string(respBody))

    var apiResp StartEnvResponse
    if err = json.Unmarshal(respBody, &apiResp); err != nil {
        return nil, fmt.Errorf("解析状态响应失败: %v", err)
    }

    return &apiResp, nil
}

// WaitForEnvironmentReady 等待环境准备就绪
func (api *MoreLoginOfficialAPI) WaitForEnvironmentReady(envID string, timeout time.Duration) error {
    log.Printf("等待环境准备就绪: %s", envID)
    start := time.Now()

    for time.Since(start) < timeout {
        status, err := api.GetEnvironmentStatus(envID)
        if err != nil {
            log.Printf("检查环境状态失败: %v", err)
            time.Sleep(3 * time.Second)
            continue
        }

        if status.Code == 0 && status.Data.Status == "running" {
            log.Printf("✓ 环境 %s 已准备就绪", envID)
            return nil
        }

        log.Printf("等待环境启动... (已等待 %.0f 秒)", time.Since(start).Seconds())
        time.Sleep(3 * time.Second)
    }

    return fmt.Errorf("等待环境启动超时 (%.0f 秒)", timeout.Seconds())
}
