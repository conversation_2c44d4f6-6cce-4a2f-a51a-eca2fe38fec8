@echo off
echo MoreLogin强制启动模式
echo ====================

REM 检查Go是否安装
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Go语言环境，请先安装Go
    pause
    exit /b 1
)

echo.
echo 强制启动模式说明:
echo - 跳过API健康检查
echo - 直接启动MoreLogin应用程序
echo - 等待30秒后尝试启动环境
echo - 即使API检查失败也会继续尝试
echo.

REM 编译程序
echo 正在编译程序...
go build -o crawler.exe .
if %errorlevel% neq 0 (
    echo 编译失败
    pause
    exit /b 1
)

echo.
echo 开始强制启动流程...
echo.

REM 运行程序
crawler.exe

pause
